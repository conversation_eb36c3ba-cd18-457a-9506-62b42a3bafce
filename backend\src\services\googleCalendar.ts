import { google } from 'googleapis';
import { GoogleAuthService } from './googleAuth';
import { CalendarEvent, AvailabilitySlot } from '../types';
import { User } from '../models/User';

export class GoogleCalendarService {
  private googleAuthService: GoogleAuthService | null = null;

  private getGoogleAuthService(): GoogleAuthService {
    if (!this.googleAuthService) {
      this.googleAuthService = new GoogleAuthService();
    }
    return this.googleAuthService;
  }

  private async getCalendarClient(user: any) {
    const authClient = this.getGoogleAuthService().getAuthenticatedClient(
      user.accessToken,
      user.refreshToken
    );

    return google.calendar({ version: 'v3', auth: authClient });
  }

  async getEvents(user: any, timeMin?: string, timeMax?: string): Promise<CalendarEvent[]> {
    try {
      const calendar = await this.getCalendarClient(user);

      const response = await calendar.events.list({
        calendarId: 'primary',
        timeMin: timeMin || new Date().toISOString(),
        timeMax: timeMax,
        maxResults: 50,
        singleEvents: true,
        orderBy: 'startTime'
      });

      return response.data.items?.map(event => ({
        id: event.id!,
        summary: event.summary || 'No Title',
        description: event.description || undefined,
        start: {
          dateTime: event.start?.dateTime || event.start?.date!,
          timeZone: event.start?.timeZone || undefined
        },
        end: {
          dateTime: event.end?.dateTime || event.end?.date!,
          timeZone: event.end?.timeZone || undefined
        },
        attendees: event.attendees?.map(attendee => ({
          email: attendee.email!,
          displayName: attendee.displayName || undefined,
          responseStatus: attendee.responseStatus || undefined
        })),
        location: event.location || undefined,
        hangoutLink: event.hangoutLink || undefined,
        status: event.status || 'confirmed'
      })) || [];
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw error;
    }
  }

  async checkAvailability(
    emails: string[],
    timeMin: string,
    timeMax: string
  ): Promise<{ [email: string]: AvailabilitySlot[] }> {
    try {
      const calendar = await this.getCalendarClient({ 
        accessToken: process.env.GOOGLE_ACCESS_TOKEN,
        refreshToken: process.env.GOOGLE_REFRESH_TOKEN 
      });

      const response = await calendar.freebusy.query({
        requestBody: {
          timeMin,
          timeMax,
          items: emails.map(email => ({ id: email }))
        }
      });

      const availability: { [email: string]: AvailabilitySlot[] } = {};

      for (const email of emails) {
        const busyTimes = response.data.calendars?.[email]?.busy || [];
        const formattedBusyTimes = busyTimes.map(busy => ({
          start: busy.start || '',
          end: busy.end || ''
        })).filter(busy => busy.start && busy.end);
        availability[email] = this.generateAvailableSlots(timeMin, timeMax, formattedBusyTimes);
      }

      return availability;
    } catch (error) {
      console.error('Error checking availability:', error);
      throw error;
    }
  }

  private generateAvailableSlots(
    timeMin: string,
    timeMax: string,
    busyTimes: Array<{ start: string; end: string }>
  ): AvailabilitySlot[] {
    const slots: AvailabilitySlot[] = [];
    const start = new Date(timeMin);
    const end = new Date(timeMax);
    
    const workingHours = { start: 9, end: 17 };
    
    for (let d = new Date(start); d < end; d.setDate(d.getDate() + 1)) {
      if (d.getDay() === 0 || d.getDay() === 6) continue;
      
      const dayStart = new Date(d);
      dayStart.setHours(workingHours.start, 0, 0, 0);
      
      const dayEnd = new Date(d);
      dayEnd.setHours(workingHours.end, 0, 0, 0);
      
      for (let hour = workingHours.start; hour < workingHours.end; hour++) {
        const slotStart = new Date(d);
        slotStart.setHours(hour, 0, 0, 0);
        
        const slotEnd = new Date(d);
        slotEnd.setHours(hour + 1, 0, 0, 0);
        
        const isAvailable = !busyTimes.some(busy => {
          const busyStart = new Date(busy.start);
          const busyEnd = new Date(busy.end);
          return slotStart < busyEnd && slotEnd > busyStart;
        });
        
        slots.push({
          start: slotStart.toISOString(),
          end: slotEnd.toISOString(),
          available: isAvailable
        });
      }
    }
    
    return slots;
  }

  async createEvent(
    user: any,
    eventData: {
      summary: string;
      description?: string;
      start: string;
      end: string;
      attendees: string[];
      location?: string;
    }
  ): Promise<CalendarEvent> {
    try {
      const calendar = await this.getCalendarClient(user);

      const event = {
        summary: eventData.summary,
        description: eventData.description,
        start: {
          dateTime: eventData.start,
          timeZone: 'UTC'
        },
        end: {
          dateTime: eventData.end,
          timeZone: 'UTC'
        },
        attendees: eventData.attendees.map(email => ({ email })),
        location: eventData.location,
        conferenceData: {
          createRequest: {
            requestId: `meet-${Date.now()}`,
            conferenceSolutionKey: { type: 'hangoutsMeet' }
          }
        }
      };

      const response = await calendar.events.insert({
        calendarId: 'primary',
        requestBody: event,
        conferenceDataVersion: 1,
        sendUpdates: 'all'
      });

      return {
        id: response.data.id!,
        summary: response.data.summary!,
        description: response.data.description || undefined,
        start: {
          dateTime: response.data.start?.dateTime!,
          timeZone: response.data.start?.timeZone || undefined
        },
        end: {
          dateTime: response.data.end?.dateTime!,
          timeZone: response.data.end?.timeZone || undefined
        },
        attendees: response.data.attendees?.map(attendee => ({
          email: attendee.email!,
          displayName: attendee.displayName || undefined,
          responseStatus: attendee.responseStatus || undefined
        })),
        location: response.data.location || undefined,
        hangoutLink: response.data.hangoutLink || undefined,
        status: response.data.status || 'confirmed'
      };
    } catch (error) {
      console.error('Error creating calendar event:', error);
      throw error;
    }
  }
}
