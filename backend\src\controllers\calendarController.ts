import { Response, NextFunction } from 'express';
import { GoogleCalendarService } from '../services/googleCalendar';
import { Meeting } from '../models/Meeting';
import { AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import { FollowUpRequest } from '../types';

let googleCalendarService: GoogleCalendarService;

const getGoogleCalendarService = () => {
  if (!googleCalendarService) {
    googleCalendarService = new GoogleCalendarService();
  }
  return googleCalendarService;
};

export const getMeetings = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const user = req.user;
    const { timeMin, timeMax } = req.query;

    const events = await getGoogleCalendarService().getEvents(
      user,
      timeMin as string,
      timeMax as string
    );

    const meetings = await Promise.all(
      events.map(async (event) => {
        let meeting = await Meeting.findOne({ googleEventId: event.id });
        
        if (!meeting) {
          meeting = new Meeting({
            googleEventId: event.id,
            userId: user._id,
            title: event.summary,
            description: event.description,
            startTime: new Date(event.start.dateTime),
            endTime: new Date(event.end.dateTime),
            attendees: event.attendees?.map(attendee => ({
              email: attendee.email,
              name: attendee.displayName,
              responseStatus: attendee.responseStatus as any
            })) || [],
            location: event.location,
            meetingLink: event.hangoutLink,
            status: new Date(event.end.dateTime) < new Date() ? 'ended' : 'upcoming'
          });
          
          await meeting.save();
        }

        return {
          id: meeting._id,
          googleEventId: event.id,
          title: event.summary,
          description: event.description,
          startTime: event.start.dateTime,
          endTime: event.end.dateTime,
          attendees: event.attendees || [],
          location: event.location,
          meetingLink: event.hangoutLink,
          status: meeting.status,
          isFollowUpScheduled: meeting.isFollowUpScheduled
        };
      })
    );

    res.status(200).json({
      success: true,
      data: meetings
    });
  } catch (error) {
    next(error);
  }
};

export const endMeeting = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { meetingId } = req.params;
    const user = req.user;

    const meeting = await Meeting.findOne({
      _id: meetingId,
      userId: user._id
    });

    if (!meeting) {
      throw createError('Meeting not found', 404);
    }

    meeting.status = 'ended';
    await meeting.save();

    res.status(200).json({
      success: true,
      data: {
        id: meeting._id,
        status: meeting.status,
        attendees: meeting.attendees,
        title: meeting.title,
        endTime: meeting.endTime
      }
    });
  } catch (error) {
    next(error);
  }
};

export const checkAvailability = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { emails, timeMin, timeMax } = req.body;

    if (!emails || !Array.isArray(emails) || emails.length === 0) {
      throw createError('Attendee emails are required', 400);
    }

    if (!timeMin || !timeMax) {
      throw createError('Time range is required', 400);
    }

    const availability = await getGoogleCalendarService().checkAvailability(
      emails,
      timeMin,
      timeMax
    );

    const mutualAvailability = [];
    const slots = availability[emails[0]] || [];

    for (const slot of slots) {
      const isAvailableForAll = emails.every(email => {
        const userSlots = availability[email] || [];
        return userSlots.some(userSlot => 
          userSlot.start === slot.start && 
          userSlot.end === slot.end && 
          userSlot.available
        );
      });

      if (isAvailableForAll && slot.available) {
        mutualAvailability.push(slot);
      }
    }

    res.status(200).json({
      success: true,
      data: {
        individual: availability,
        mutual: mutualAvailability
      }
    });
  } catch (error) {
    next(error);
  }
};

export const createFollowUpMeeting = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const user = req.user;
    const {
      originalMeetingId,
      attendees,
      start,
      end,
      title,
      description
    }: FollowUpRequest & { start: string; end: string } = req.body;

    const originalMeeting = await Meeting.findOne({
      _id: originalMeetingId,
      userId: user._id
    });

    if (!originalMeeting) {
      throw createError('Original meeting not found', 404);
    }

    const eventData = {
      summary: title || `Follow-up: ${originalMeeting.title}`,
      description: description || `Follow-up meeting for: ${originalMeeting.title}`,
      start,
      end,
      attendees: attendees || originalMeeting.attendees.map(a => a.email)
    };

    const createdEvent = await getGoogleCalendarService().createEvent(user, eventData);

    const followUpMeeting = new Meeting({
      googleEventId: createdEvent.id,
      userId: user._id,
      title: createdEvent.summary,
      description: createdEvent.description,
      startTime: new Date(createdEvent.start.dateTime),
      endTime: new Date(createdEvent.end.dateTime),
      attendees: createdEvent.attendees?.map(attendee => ({
        email: attendee.email,
        name: attendee.displayName,
        responseStatus: attendee.responseStatus as any
      })) || [],
      location: createdEvent.location,
      meetingLink: createdEvent.hangoutLink,
      originalMeetingId: originalMeeting._id
    });

    await followUpMeeting.save();

    originalMeeting.isFollowUpScheduled = true;
    await originalMeeting.save();

    res.status(201).json({
      success: true,
      data: {
        id: followUpMeeting._id,
        googleEventId: createdEvent.id,
        title: createdEvent.summary,
        startTime: createdEvent.start.dateTime,
        endTime: createdEvent.end.dateTime,
        attendees: createdEvent.attendees,
        meetingLink: createdEvent.hangoutLink
      }
    });
  } catch (error) {
    next(error);
  }
};
