import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { GoogleAuthService } from '../services/googleAuth';
import { User } from '../models/User';
import { createError } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';

let googleAuthService: GoogleAuthService;

const getGoogleAuthService = () => {
  if (!googleAuthService) {
    googleAuthService = new GoogleAuthService();
  }
  return googleAuthService;
};

export const googleAuth = (req: Request, res: Response): void => {
  const authUrl = getGoogleAuthService().getAuthUrl();
  res.redirect(authUrl);
};

export const googleCallback = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { code } = req.query;

    if (!code || typeof code !== 'string') {
      throw createError('Authorization code not provided', 400);
    }

    const tokens = await getGoogleAuthService().getTokens(code);
    const userInfo = await getGoogleAuthService().getUserInfo(tokens.access_token);

    let user = await User.findOne({ googleId: userInfo.id });

    if (!user) {
      user = new User({
        googleId: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        tokenExpiry: new Date(tokens.expiry_date)
      });
    } else {
      user.accessToken = tokens.access_token;
      if (tokens.refresh_token) {
        user.refreshToken = tokens.refresh_token;
      }
      user.tokenExpiry = new Date(tokens.expiry_date);
      user.name = userInfo.name;
      user.picture = userInfo.picture;
    }

    await user.save();

    const jwtToken = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );

    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    res.redirect(`${frontendUrl}/auth/callback?token=${jwtToken}`);
  } catch (error) {
    next(error);
  }
};

export const logout = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    next(error);
  }
};

export const getProfile = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const user = req.user;
    
    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        email: user.email,
        name: user.name,
        picture: user.picture
      }
    });
  } catch (error) {
    next(error);
  }
};
