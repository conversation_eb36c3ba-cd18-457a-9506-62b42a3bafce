import { google } from 'googleapis';
import { GoogleTokens, GoogleUserInfo } from '../types';

export class GoogleAuthService {
  private oauth2Client;

  constructor() {
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    const redirectUri = process.env.GOOGLE_REDIRECT_URI;

    console.log('Environment variables check:');
    console.log('GOOGLE_CLIENT_ID:', clientId ? 'Present' : 'Missing');
    console.log('GOOGLE_CLIENT_SECRET:', clientSecret ? 'Present' : 'Missing');
    console.log('GOOGLE_REDIRECT_URI:', redirectUri ? 'Present' : 'Missing');

    if (!clientId || !clientSecret || !redirectUri) {
      throw new Error(`Missing Google OAuth environment variables: clientId=${!!clientId}, clientSecret=${!!clientSecret}, redirectUri=${!!redirectUri}`);
    }

    this.oauth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      redirectUri
    );
  }

  getAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/calendar'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent'
    });
  }

  async getTokens(code: string): Promise<GoogleTokens> {
    const { tokens } = await this.oauth2Client.getToken(code);
    return tokens as GoogleTokens;
  }

  async getUserInfo(accessToken: string): Promise<GoogleUserInfo> {
    this.oauth2Client.setCredentials({ access_token: accessToken });
    
    const oauth2 = google.oauth2({ version: 'v2', auth: this.oauth2Client });
    const { data } = await oauth2.userinfo.get();
    
    return data as GoogleUserInfo;
  }

  async refreshAccessToken(refreshToken: string): Promise<GoogleTokens> {
    this.oauth2Client.setCredentials({ refresh_token: refreshToken });
    
    const { credentials } = await this.oauth2Client.refreshAccessToken();
    return credentials as GoogleTokens;
  }

  getAuthenticatedClient(accessToken: string, refreshToken: string) {
    const client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );

    client.setCredentials({
      access_token: accessToken,
      refresh_token: refreshToken
    });

    return client;
  }
}
